2025-06-15 09:05:34,939 - __main__ - INFO - 💰 Initializing Personal Trading Test
2025-06-15 09:05:34,939 - __main__ - INFO -    Account: Your personal Cashcoldgame Phemex
2025-06-15 09:05:34,939 - __main__ - INFO -    Purpose: Test trading algorithms safely
2025-06-15 09:05:34,940 - services.personal_phemex_service - INFO - Loaded personal environment variables from .env.personal
2025-06-15 09:05:34,941 - services.phemex_service - INFO - API Key: 42b00... Secret: wOekj...
2025-06-15 09:05:34,945 - services.phemex_service - INFO - Initialized Phemex service with production API
2025-06-15 09:05:34,945 - services.personal_phemex_service - INFO - Initialized Personal Phemex service
2025-06-15 09:05:34,945 - services.personal_phemex_service - INFO - Account: PERSONAL_CASHCOLDGAME
2025-06-15 09:05:34,945 - services.personal_phemex_service - INFO - Default Leverage: 5x
2025-06-15 09:05:34,945 - services.personal_phemex_service - INFO - BTC Position Size: 1.0%
2025-06-15 09:05:34,945 - services.personal_phemex_service - INFO - ADA Position Size: 0.5%
2025-06-15 09:05:34,945 - __main__ - INFO - ✅ Personal Trading Test initialized
2025-06-15 09:05:34,945 - __main__ - INFO -    BTC Threshold: 0.4
2025-06-15 09:05:34,945 - __main__ - INFO -    ADA Threshold: 0.4
2025-06-15 09:05:34,945 - __main__ - INFO -    Trades File: trades_personal.csv
2025-06-15 09:05:34,945 - __main__ - INFO - 📝 Created trades file: trades_personal.csv
2025-06-15 09:05:34,946 - __main__ - INFO - 💰 Checking account balance...
2025-06-15 09:05:37,522 - __main__ - INFO - 💰 Account Status:
2025-06-15 09:05:37,522 - __main__ - INFO -    Free USDT: $152.61
2025-06-15 09:05:37,522 - __main__ - INFO -    Total USDT: $152.61
2025-06-15 09:05:37,522 - __main__ - INFO -    Open Positions: 0
2025-06-15 09:06:32,495 - scripts.run_personal_trading_test - INFO - 💰 Initializing Personal Trading Test
2025-06-15 09:06:32,495 - scripts.run_personal_trading_test - INFO -    Account: Your personal Cashcoldgame Phemex
2025-06-15 09:06:32,495 - scripts.run_personal_trading_test - INFO -    Purpose: Test trading algorithms safely
2025-06-15 09:06:32,497 - services.personal_phemex_service - INFO - Loaded personal environment variables from .env.personal
2025-06-15 09:06:32,497 - services.phemex_service - INFO - API Key: 42b00... Secret: wOekj...
2025-06-15 09:06:32,501 - services.phemex_service - INFO - Initialized Phemex service with production API
2025-06-15 09:06:32,501 - services.personal_phemex_service - INFO - Initialized Personal Phemex service
2025-06-15 09:06:32,501 - services.personal_phemex_service - INFO - Account: PERSONAL_CASHCOLDGAME
2025-06-15 09:06:32,501 - services.personal_phemex_service - INFO - Default Leverage: 5x
2025-06-15 09:06:32,501 - services.personal_phemex_service - INFO - BTC Position Size: 1.0%
2025-06-15 09:06:32,501 - services.personal_phemex_service - INFO - ADA Position Size: 0.5%
2025-06-15 09:06:32,502 - scripts.run_personal_trading_test - INFO - ✅ Personal Trading Test initialized
2025-06-15 09:06:32,502 - scripts.run_personal_trading_test - INFO -    BTC Threshold: 0.4
2025-06-15 09:06:32,502 - scripts.run_personal_trading_test - INFO -    ADA Threshold: 0.4
2025-06-15 09:06:32,502 - scripts.run_personal_trading_test - INFO -    Trades File: trades_personal.csv
2025-06-15 09:06:32,502 - scripts.run_personal_trading_test - INFO - 📊 Analyzing BTC with backtested algorithm
2025-06-15 09:06:32,502 - services.market_data_service - INFO - Fetching BTC 1d data from 2025-03-17 to 2025-06-15
2025-06-15 09:06:32,715 - services.market_data_service - INFO - Fetched 90 candles
2025-06-15 09:06:32,715 - services.market_data_service - INFO - Fetching BTC 4h data from 2025-03-17 to 2025-06-15
2025-06-15 09:06:32,937 - services.market_data_service - INFO - Fetched 539 candles
2025-06-15 09:06:32,938 - services.market_data_service - INFO - Fetching BTC 1h data from 2025-03-17 to 2025-06-15
2025-06-15 09:06:33,141 - services.market_data_service - INFO - Fetched 707 candles
2025-06-15 09:06:35,316 - scripts.run_personal_trading_test - INFO - 📈 BTC Analysis:
2025-06-15 09:06:35,316 - scripts.run_personal_trading_test - INFO -    Price: $105689.6000
2025-06-15 09:06:35,316 - scripts.run_personal_trading_test - INFO -    Combined Trend: 0.060
2025-06-15 09:06:35,316 - scripts.run_personal_trading_test - INFO -    Threshold: 0.4
2025-06-15 09:06:35,316 - scripts.run_personal_trading_test - INFO -    Signal: NEUTRAL
2025-06-15 09:06:35,316 - scripts.run_personal_trading_test - INFO -    Confidence: 0.0%
2025-06-15 09:06:35,316 - scripts.run_personal_trading_test - INFO - 📊 No signal: trend 0.060 below threshold 0.4
2025-06-15 09:06:35,316 - scripts.run_personal_trading_test - INFO - 📊 Analyzing ADA with backtested algorithm
2025-06-15 09:06:35,316 - services.market_data_service - INFO - Fetching ADA 1d data from 2025-03-17 to 2025-06-15
2025-06-15 09:06:35,321 - services.market_data_service - INFO - Loaded 15 rows from data_seed
2025-06-15 09:06:35,321 - services.market_data_service - INFO - Fetching ADA 1h data from 2025-03-17 to 2025-06-15
2025-06-15 09:06:35,352 - services.market_data_service - INFO - Loaded 359 rows from data_seed
2025-06-15 09:06:35,352 - services.market_data_service - INFO - Fetching ADA 15m data from 2025-03-17 to 2025-06-15
2025-06-15 09:06:35,449 - services.market_data_service - INFO - Loaded 1317 rows from data_seed
2025-06-15 09:06:39,726 - scripts.run_personal_trading_test - INFO - 📈 ADA Analysis:
2025-06-15 09:06:39,726 - scripts.run_personal_trading_test - INFO -    Price: $0.6618
2025-06-15 09:06:39,726 - scripts.run_personal_trading_test - INFO -    Combined Trend: -0.275
2025-06-15 09:06:39,726 - scripts.run_personal_trading_test - INFO -    Threshold: 0.4
2025-06-15 09:06:39,727 - scripts.run_personal_trading_test - INFO -    Signal: NEUTRAL
2025-06-15 09:06:39,727 - scripts.run_personal_trading_test - INFO -    Confidence: 0.0%
2025-06-15 09:06:39,727 - scripts.run_personal_trading_test - INFO - 📊 No signal: trend -0.275 below threshold 0.4
