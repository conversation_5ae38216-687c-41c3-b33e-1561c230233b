2025-06-12 16:44:52,211 - __main__ - INFO - 🚀 Starting TIER 1: AXON Signal Service
2025-06-12 16:44:52,211 - __main__ - INFO - ============================================================
2025-06-12 16:44:52,211 - __main__ - INFO - PURPOSE: Information Only - NO trade execution
2025-06-12 16:44:52,211 - __main__ - INFO - THRESHOLDS: Original backtested parameters (0.4)
2025-06-12 16:44:52,211 - __main__ - INFO - DESTINATION: AXON AI frontend for monitoring
2025-06-12 16:44:52,211 - __main__ - INFO - INTERVAL: Every 5 minutes
2025-06-12 16:44:52,211 - __main__ - INFO - ============================================================
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO - 🔍 Initializing AXON Signal Service (TIER 1 - Information Only)
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO - 🔧 Restored original backtested thresholds:
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO -    BTC: 0.4 (original backtested)
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO -    ADA: 0.4 (original backtested)
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO - ✅ AXON Signal Service initialized with ORIGINAL backtested thresholds
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO -    BTC Model Threshold: 0.4
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO -    ADA Model Threshold: 0.4
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO - 🚀 Starting AXON Signal Service (TIER 1)
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO -    Purpose: Information Only - NO trade execution
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO -    Interval: 5 minutes
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO -    Thresholds: Original backtested parameters
2025-06-12 16:44:52,211 - services.axon_signal_service - INFO - 🔄 Starting AXON Signal Service analysis cycle
2025-06-12 16:44:52,212 - services.axon_signal_service - INFO - 🔍 Analyzing BTC with original backtested algorithm
2025-06-12 16:44:52,212 - services.market_data_service - INFO - Fetching BTC 1d data from 2025-03-14 to 2025-06-12
2025-06-12 16:44:52,496 - services.market_data_service - INFO - Fetched 90 candles
2025-06-12 16:44:52,496 - services.market_data_service - INFO - Fetching BTC 4h data from 2025-03-14 to 2025-06-12
2025-06-12 16:44:52,918 - services.market_data_service - INFO - Fetched 539 candles
2025-06-12 16:44:52,919 - services.market_data_service - INFO - Fetching BTC 1h data from 2025-03-14 to 2025-06-12
2025-06-12 16:44:53,288 - services.market_data_service - INFO - Fetched 700 candles
2025-06-12 16:44:55,221 - services.axon_signal_service - INFO - 📊 BTC Analysis: NO_SIGNAL - No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.
2025-06-12 16:44:55,569 - services.axon_signal_service - WARNING - ⚠️ AXON AI response: 422 - {"detail":[{"type":"int_parsing","loc":["body","timestamp"],"msg":"Input should be a valid integer, unable to parse string as an integer","input":"2025-06-12T16:44:55.221091"},{"type":"missing","loc":["body","status"],"msg":"Field required","input":{"bot_name":"axon_btc","symbol":"BTC","signal_type":"NEUTRAL","confidence":0.0,"price":108735.1,"reasoning":"No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:44:55.221091","model_info":{"name":"TITAN2K (Original)","threshold":0.4,"combined_trend":0.09999999999999998,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","confidence_level"],"msg":"Field required","input":{"bot_name":"axon_btc","symbol":"BTC","signal_type":"NEUTRAL","confidence":0.0,"price":108735.1,"reasoning":"No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:44:55.221091","model_info":{"name":"TITAN2K (Original)","threshold":0.4,"combined_trend":0.09999999999999998,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","next_action"],"msg":"Field required","input":{"bot_name":"axon_btc","symbol":"BTC","signal_type":"NEUTRAL","confidence":0.0,"price":108735.1,"reasoning":"No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:44:55.221091","model_info":{"name":"TITAN2K (Original)","threshold":0.4,"combined_trend":0.09999999999999998,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","risk_assessment"],"msg":"Field required","input":{"bot_name":"axon_btc","symbol":"BTC","signal_type":"NEUTRAL","confidence":0.0,"price":108735.1,"reasoning":"No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:44:55.221091","model_info":{"name":"TITAN2K (Original)","threshold":0.4,"combined_trend":0.09999999999999998,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}}]}
2025-06-12 16:44:55,571 - services.axon_signal_service - INFO - 🔍 Analyzing ADA with original backtested algorithm
2025-06-12 16:44:55,571 - services.market_data_service - INFO - Fetching ADA 1d data from 2025-03-14 to 2025-06-12
2025-06-12 16:44:55,576 - services.market_data_service - INFO - Loaded 18 rows from data_seed
2025-06-12 16:44:55,576 - services.market_data_service - INFO - Fetching ADA 1h data from 2025-03-14 to 2025-06-12
2025-06-12 16:44:55,604 - services.market_data_service - INFO - Loaded 431 rows from data_seed
2025-06-12 16:44:55,604 - services.market_data_service - INFO - Fetching ADA 15m data from 2025-03-14 to 2025-06-12
2025-06-12 16:44:55,694 - services.market_data_service - INFO - Loaded 1579 rows from data_seed
2025-06-12 16:45:00,318 - services.axon_signal_service - INFO - 📊 ADA Analysis: NO_SIGNAL - No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.
2025-06-12 16:45:00,457 - services.axon_signal_service - WARNING - ⚠️ AXON AI response: 422 - {"detail":[{"type":"int_parsing","loc":["body","timestamp"],"msg":"Input should be a valid integer, unable to parse string as an integer","input":"2025-06-12T16:45:00.318145"},{"type":"missing","loc":["body","status"],"msg":"Field required","input":{"bot_name":"axon_ada","symbol":"ADA","signal_type":"NEUTRAL","confidence":0.0,"price":0.661758,"reasoning":"No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:00.318145","model_info":{"name":"TITAN2K Trend-Tuned","threshold":0.4,"combined_trend":-0.27499999999999997,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","confidence_level"],"msg":"Field required","input":{"bot_name":"axon_ada","symbol":"ADA","signal_type":"NEUTRAL","confidence":0.0,"price":0.661758,"reasoning":"No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:00.318145","model_info":{"name":"TITAN2K Trend-Tuned","threshold":0.4,"combined_trend":-0.27499999999999997,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","next_action"],"msg":"Field required","input":{"bot_name":"axon_ada","symbol":"ADA","signal_type":"NEUTRAL","confidence":0.0,"price":0.661758,"reasoning":"No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:00.318145","model_info":{"name":"TITAN2K Trend-Tuned","threshold":0.4,"combined_trend":-0.27499999999999997,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","risk_assessment"],"msg":"Field required","input":{"bot_name":"axon_ada","symbol":"ADA","signal_type":"NEUTRAL","confidence":0.0,"price":0.661758,"reasoning":"No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:00.318145","model_info":{"name":"TITAN2K Trend-Tuned","threshold":0.4,"combined_trend":-0.27499999999999997,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}}]}
2025-06-12 16:45:00,460 - services.axon_signal_service - INFO - ✅ AXON Signal Service analysis cycle completed
2025-06-12 16:45:00,460 - services.axon_signal_service - INFO - ⏳ Waiting 5 minutes until next analysis
2025-06-12 16:45:27,364 - services.axon_signal_service - INFO - 🔍 Initializing AXON Signal Service (TIER 1 - Information Only)
2025-06-12 16:45:27,365 - services.axon_signal_service - INFO - 🔧 Restored original backtested thresholds:
2025-06-12 16:45:27,365 - services.axon_signal_service - INFO -    BTC: 0.4 (original backtested)
2025-06-12 16:45:27,365 - services.axon_signal_service - INFO -    ADA: 0.4 (original backtested)
2025-06-12 16:45:27,365 - services.axon_signal_service - INFO - ✅ AXON Signal Service initialized with ORIGINAL backtested thresholds
2025-06-12 16:45:27,365 - services.axon_signal_service - INFO -    BTC Model Threshold: 0.4
2025-06-12 16:45:27,365 - services.axon_signal_service - INFO -    ADA Model Threshold: 0.4
2025-06-12 16:45:27,365 - services.axon_signal_service - INFO - 🔄 Starting AXON Signal Service analysis cycle
2025-06-12 16:45:27,365 - services.axon_signal_service - INFO - 🔍 Analyzing BTC with original backtested algorithm
2025-06-12 16:45:27,365 - services.market_data_service - INFO - Fetching BTC 1d data from 2025-03-14 to 2025-06-12
2025-06-12 16:45:27,757 - services.market_data_service - INFO - Fetched 90 candles
2025-06-12 16:45:27,757 - services.market_data_service - INFO - Fetching BTC 4h data from 2025-03-14 to 2025-06-12
2025-06-12 16:45:28,054 - services.market_data_service - INFO - Fetched 539 candles
2025-06-12 16:45:28,054 - services.market_data_service - INFO - Fetching BTC 1h data from 2025-03-14 to 2025-06-12
2025-06-12 16:45:28,413 - services.market_data_service - INFO - Fetched 700 candles
2025-06-12 16:45:30,421 - services.axon_signal_service - INFO - 📊 BTC Analysis: NO_SIGNAL - No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.
2025-06-12 16:45:30,498 - services.axon_signal_service - WARNING - ⚠️ AXON AI response: 422 - {"detail":[{"type":"int_parsing","loc":["body","timestamp"],"msg":"Input should be a valid integer, unable to parse string as an integer","input":"2025-06-12T16:45:30.421692"},{"type":"missing","loc":["body","status"],"msg":"Field required","input":{"bot_name":"axon_btc","symbol":"BTC","signal_type":"NEUTRAL","confidence":0.0,"price":108735.1,"reasoning":"No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:30.421692","model_info":{"name":"TITAN2K (Original)","threshold":0.4,"combined_trend":0.09999999999999998,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","confidence_level"],"msg":"Field required","input":{"bot_name":"axon_btc","symbol":"BTC","signal_type":"NEUTRAL","confidence":0.0,"price":108735.1,"reasoning":"No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:30.421692","model_info":{"name":"TITAN2K (Original)","threshold":0.4,"combined_trend":0.09999999999999998,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","next_action"],"msg":"Field required","input":{"bot_name":"axon_btc","symbol":"BTC","signal_type":"NEUTRAL","confidence":0.0,"price":108735.1,"reasoning":"No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:30.421692","model_info":{"name":"TITAN2K (Original)","threshold":0.4,"combined_trend":0.09999999999999998,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","risk_assessment"],"msg":"Field required","input":{"bot_name":"axon_btc","symbol":"BTC","signal_type":"NEUTRAL","confidence":0.0,"price":108735.1,"reasoning":"No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:30.421692","model_info":{"name":"TITAN2K (Original)","threshold":0.4,"combined_trend":0.09999999999999998,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}}]}
2025-06-12 16:45:30,499 - services.axon_signal_service - INFO - 🔍 Analyzing ADA with original backtested algorithm
2025-06-12 16:45:30,499 - services.market_data_service - INFO - Fetching ADA 1d data from 2025-03-14 to 2025-06-12
2025-06-12 16:45:30,504 - services.market_data_service - INFO - Loaded 18 rows from data_seed
2025-06-12 16:45:30,504 - services.market_data_service - INFO - Fetching ADA 1h data from 2025-03-14 to 2025-06-12
2025-06-12 16:45:30,534 - services.market_data_service - INFO - Loaded 431 rows from data_seed
2025-06-12 16:45:30,534 - services.market_data_service - INFO - Fetching ADA 15m data from 2025-03-14 to 2025-06-12
2025-06-12 16:45:30,628 - services.market_data_service - INFO - Loaded 1579 rows from data_seed
2025-06-12 16:45:35,325 - services.axon_signal_service - INFO - 📊 ADA Analysis: NO_SIGNAL - No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.
2025-06-12 16:45:35,407 - services.axon_signal_service - WARNING - ⚠️ AXON AI response: 422 - {"detail":[{"type":"int_parsing","loc":["body","timestamp"],"msg":"Input should be a valid integer, unable to parse string as an integer","input":"2025-06-12T16:45:35.325034"},{"type":"missing","loc":["body","status"],"msg":"Field required","input":{"bot_name":"axon_ada","symbol":"ADA","signal_type":"NEUTRAL","confidence":0.0,"price":0.661758,"reasoning":"No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:35.325034","model_info":{"name":"TITAN2K Trend-Tuned","threshold":0.4,"combined_trend":-0.27499999999999997,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","confidence_level"],"msg":"Field required","input":{"bot_name":"axon_ada","symbol":"ADA","signal_type":"NEUTRAL","confidence":0.0,"price":0.661758,"reasoning":"No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:35.325034","model_info":{"name":"TITAN2K Trend-Tuned","threshold":0.4,"combined_trend":-0.27499999999999997,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","next_action"],"msg":"Field required","input":{"bot_name":"axon_ada","symbol":"ADA","signal_type":"NEUTRAL","confidence":0.0,"price":0.661758,"reasoning":"No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:35.325034","model_info":{"name":"TITAN2K Trend-Tuned","threshold":0.4,"combined_trend":-0.27499999999999997,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}},{"type":"missing","loc":["body","risk_assessment"],"msg":"Field required","input":{"bot_name":"axon_ada","symbol":"ADA","signal_type":"NEUTRAL","confidence":0.0,"price":0.661758,"reasoning":"No signal: Combined trend (-0.275) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.","market_condition":"NO_SIGNAL","timestamp":"2025-06-12T16:45:35.325034","model_info":{"name":"TITAN2K Trend-Tuned","threshold":0.4,"combined_trend":-0.27499999999999997,"backtested_parameters":true,"tier":"AXON_SIGNAL_SERVICE"}}}]}
2025-06-12 16:45:35,409 - services.axon_signal_service - INFO - ✅ AXON Signal Service analysis cycle completed
2025-06-12 16:46:03,181 - services.axon_signal_service - INFO - 🔍 Initializing AXON Signal Service (TIER 1 - Information Only)
2025-06-12 16:46:03,181 - services.axon_signal_service - INFO - 🔧 Restored original backtested thresholds:
2025-06-12 16:46:03,181 - services.axon_signal_service - INFO -    BTC: 0.4 (original backtested)
2025-06-12 16:46:03,181 - services.axon_signal_service - INFO -    ADA: 0.4 (original backtested)
2025-06-12 16:46:03,181 - services.axon_signal_service - INFO - ✅ AXON Signal Service initialized with ORIGINAL backtested thresholds
2025-06-12 16:46:03,181 - services.axon_signal_service - INFO -    BTC Model Threshold: 0.4
2025-06-12 16:46:03,181 - services.axon_signal_service - INFO -    ADA Model Threshold: 0.4
2025-06-12 16:46:03,181 - services.axon_signal_service - INFO - 🔍 Analyzing BTC with original backtested algorithm
2025-06-12 16:46:03,182 - services.market_data_service - INFO - Fetching BTC 1d data from 2025-03-14 to 2025-06-12
2025-06-12 16:46:03,523 - services.market_data_service - INFO - Fetched 90 candles
2025-06-12 16:46:03,523 - services.market_data_service - INFO - Fetching BTC 4h data from 2025-03-14 to 2025-06-12
2025-06-12 16:46:03,907 - services.market_data_service - INFO - Fetched 539 candles
2025-06-12 16:46:03,907 - services.market_data_service - INFO - Fetching BTC 1h data from 2025-03-14 to 2025-06-12
2025-06-12 16:46:04,270 - services.market_data_service - INFO - Fetched 700 candles
2025-06-12 16:46:06,221 - services.axon_signal_service - INFO - 📊 BTC Analysis: NO_SIGNAL - No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.
2025-06-12 16:46:06,343 - services.axon_signal_service - WARNING - ⚠️ AXON AI response: 422 - {"detail":[{"type":"float_parsing","loc":["body","confidence_level"],"msg":"Input should be a valid number, unable to parse string as a number","input":"LOW"}]}
2025-06-12 16:46:27,415 - services.axon_signal_service - INFO - 🔍 Initializing AXON Signal Service (TIER 1 - Information Only)
2025-06-12 16:46:27,415 - services.axon_signal_service - INFO - 🔧 Restored original backtested thresholds:
2025-06-12 16:46:27,415 - services.axon_signal_service - INFO -    BTC: 0.4 (original backtested)
2025-06-12 16:46:27,415 - services.axon_signal_service - INFO -    ADA: 0.4 (original backtested)
2025-06-12 16:46:27,415 - services.axon_signal_service - INFO - ✅ AXON Signal Service initialized with ORIGINAL backtested thresholds
2025-06-12 16:46:27,415 - services.axon_signal_service - INFO -    BTC Model Threshold: 0.4
2025-06-12 16:46:27,415 - services.axon_signal_service - INFO -    ADA Model Threshold: 0.4
2025-06-12 16:46:27,415 - services.axon_signal_service - INFO - 🔍 Analyzing BTC with original backtested algorithm
2025-06-12 16:46:27,416 - services.market_data_service - INFO - Fetching BTC 1d data from 2025-03-14 to 2025-06-12
2025-06-12 16:46:27,641 - services.market_data_service - INFO - Fetched 90 candles
2025-06-12 16:46:27,641 - services.market_data_service - INFO - Fetching BTC 4h data from 2025-03-14 to 2025-06-12
2025-06-12 16:46:27,861 - services.market_data_service - INFO - Fetched 539 candles
2025-06-12 16:46:27,862 - services.market_data_service - INFO - Fetching BTC 1h data from 2025-03-14 to 2025-06-12
2025-06-12 16:46:28,298 - services.market_data_service - INFO - Fetched 700 candles
2025-06-12 16:46:30,296 - services.axon_signal_service - INFO - 📊 BTC Analysis: NO_SIGNAL - No signal: Combined trend (0.100) below threshold (0.400). Market conditions not favorable for entry. Using original backtested parameters.
2025-06-12 16:46:30,529 - services.axon_signal_service - INFO - ✅ Successfully sent BTC analysis to AXON AI
