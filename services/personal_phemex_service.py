"""
Personal Phemex Service for testing trading algorithms
Uses your personal "Cashcoldgame" Phemex account for safe testing
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from services.phemex_service import PhemexService

logger = logging.getLogger(__name__)

class PersonalPhemexService:
    """Personal Phemex service for testing with your own account."""
    
    def __init__(self):
        """Initialize personal Phemex service."""
        # Load personal environment variables
        load_dotenv('.env.personal')
        logger.info("Loaded personal environment variables from .env.personal")
        
        # Initialize base Phemex service
        self.phemex_service = PhemexService()
        self.exchange = self.phemex_service.exchange
        
        # Personal account settings
        self.account_name = "PERSONAL_CASHCOLDGAME"
        self.default_leverage = int(os.getenv('DEFAULT_LEVERAGE', '1'))  # Conservative default
        self.btc_position_percent = float(os.getenv('BTC_POSITION_SIZE_PERCENT', '1.0'))
        self.ada_position_percent = float(os.getenv('ADA_POSITION_SIZE_PERCENT', '0.5'))
        
        logger.info(f"Initialized Personal Phemex service")
        logger.info(f"Account: {self.account_name}")
        logger.info(f"Default Leverage: {self.default_leverage}x")
        logger.info(f"BTC Position Size: {self.btc_position_percent}%")
        logger.info(f"ADA Position Size: {self.ada_position_percent}%")
    
    def calculate_position_size(self, symbol: str, price: float, risk_percent: float = None) -> float:
        """Calculate safe position size for testing."""
        try:
            balance = self.exchange.fetch_balance()
            free_balance = balance['free']['USDT']
            
            # Use symbol-specific risk or default
            if risk_percent is None:
                if symbol == "BTC":
                    risk_percent = self.btc_position_percent / 100
                else:  # ADA
                    risk_percent = self.ada_position_percent / 100
            else:
                risk_percent = risk_percent / 100
            
            # Calculate position value
            position_value = free_balance * risk_percent
            
            # Calculate quantity
            quantity = position_value / price
            
            # Apply minimum order sizes
            if symbol == "BTC":
                min_quantity = 0.00002  # Phemex minimum for BTC
                if quantity < min_quantity:
                    quantity = min_quantity
            elif symbol == "ADA":
                min_quantity = 2.0  # Phemex minimum for ADA
                if quantity < min_quantity:
                    quantity = min_quantity
            
            logger.info(f"💰 Position sizing for {symbol}:")
            logger.info(f"   Free Balance: ${free_balance:.2f}")
            logger.info(f"   Risk: {risk_percent*100:.1f}%")
            logger.info(f"   Position Value: ${position_value:.2f}")
            logger.info(f"   Price: ${price:.4f}")
            logger.info(f"   Quantity: {quantity:.6f} {symbol}")
            
            return quantity
            
        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            # Return safe minimums
            return 0.00002 if symbol == "BTC" else 2.0
    
    def place_order(self, symbol: str, side: str, amount: float, price: float = None, order_type: str = "market") -> Dict[str, Any]:
        """Place order using correct CCXT parameters."""
        try:
            symbol_pair = f"{symbol}/USDT"
            
            logger.info(f"🔄 Placing {order_type} {side} order:")
            logger.info(f"   Symbol: {symbol_pair}")
            logger.info(f"   Amount: {amount:.6f}")
            logger.info(f"   Price: ${price:.4f}" if price else "Market Price")
            
            if order_type == "market":
                if side.upper() == "BUY":
                    order = self.exchange.create_market_buy_order(
                        symbol=symbol_pair,
                        amount=amount
                    )
                else:  # SELL
                    order = self.exchange.create_market_sell_order(
                        symbol=symbol_pair,
                        amount=amount
                    )
            else:  # limit order
                order = self.exchange.create_limit_order(
                    symbol=symbol_pair,
                    side=side.lower(),
                    amount=amount,
                    price=price
                )
            
            if order and order.get("id"):
                logger.info(f"✅ Order placed successfully:")
                logger.info(f"   Order ID: {order['id']}")
                logger.info(f"   Status: {order.get('status', 'Unknown')}")
                logger.info(f"   Amount: {order.get('amount', amount)}")
                logger.info(f"   Price: ${order.get('price', price)}")
                
                return {
                    "success": True,
                    "order_id": order["id"],
                    "status": order.get("status"),
                    "amount": order.get("amount", amount),
                    "price": order.get("price", price),
                    "symbol": symbol_pair,
                    "side": side,
                    "order": order
                }
            else:
                logger.error("❌ Order failed: No order ID returned")
                return {
                    "success": False,
                    "error": "No order ID returned",
                    "order": order
                }
                
        except Exception as e:
            logger.error(f"❌ Order failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_account_balance(self) -> Dict[str, Any]:
        """Get account balance information."""
        try:
            balance = self.exchange.fetch_balance()
            
            return {
                "free_usdt": balance['free']['USDT'],
                "used_usdt": balance['used']['USDT'],
                "total_usdt": balance['total']['USDT'],
                "free_btc": balance['free'].get('BTC', 0),
                "free_ada": balance['free'].get('ADA', 0),
                "account": self.account_name
            }
            
        except Exception as e:
            logger.error(f"Error getting balance: {str(e)}")
            return {"error": str(e)}
    
    def get_open_positions(self) -> Dict[str, Any]:
        """Get open positions."""
        try:
            positions = self.exchange.fetch_positions()
            open_positions = [pos for pos in positions if pos['contracts'] > 0]
            
            return {
                "positions": open_positions,
                "count": len(open_positions)
            }
            
        except Exception as e:
            logger.error(f"Error getting positions: {str(e)}")
            return {"error": str(e), "positions": [], "count": 0}
    
    def close_position(self, symbol: str) -> Dict[str, Any]:
        """Close an open position."""
        try:
            symbol_pair = f"{symbol}/USDT"
            positions = self.exchange.fetch_positions([symbol_pair])
            
            for position in positions:
                if position['contracts'] > 0:
                    # Close position by placing opposite order
                    side = "sell" if position['side'] == 'long' else "buy"
                    amount = position['contracts']
                    
                    order = self.exchange.create_market_order(
                        symbol=symbol_pair,
                        side=side,
                        amount=amount
                    )
                    
                    logger.info(f"✅ Closed {symbol} position")
                    return {"success": True, "order": order}
            
            return {"success": False, "error": f"No open position for {symbol}"}
            
        except Exception as e:
            logger.error(f"Error closing position: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def execute_trade(self, symbol: str, signal: str, price: float, confidence: float) -> Dict[str, Any]:
        """Execute a trade based on signal."""
        try:
            logger.info(f"🚀 Executing {signal} trade for {symbol}")
            
            # Calculate position size
            quantity = self.calculate_position_size(symbol, price)
            
            # Place order
            result = self.place_order(symbol, signal, quantity, price)
            
            if result.get("success"):
                logger.info(f"✅ Trade executed successfully for {symbol}")
                return {
                    "status": "success",
                    "symbol": symbol,
                    "signal": signal,
                    "quantity": quantity,
                    "price": price,
                    "confidence": confidence,
                    "order_id": result.get("order_id"),
                    "account": self.account_name
                }
            else:
                logger.error(f"❌ Trade failed for {symbol}: {result.get('error')}")
                return {
                    "status": "error",
                    "symbol": symbol,
                    "signal": signal,
                    "error": result.get("error"),
                    "account": self.account_name
                }
                
        except Exception as e:
            logger.error(f"Error executing trade: {str(e)}")
            return {
                "status": "error",
                "symbol": symbol,
                "signal": signal,
                "error": str(e),
                "account": self.account_name
            }
