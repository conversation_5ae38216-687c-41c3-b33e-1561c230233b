"""
Personal Phemex Service for testing trading algorithms
Uses your personal "Cashcoldgame" Phemex account for safe testing
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from services.phemex_service import PhemexService

logger = logging.getLogger(__name__)

class PersonalPhemexService:
    """Personal Phemex service for testing with your own account."""
    
    def __init__(self):
        """Initialize personal Phemex service."""
        # Load personal environment variables FIRST
        load_dotenv('.env.personal', override=True)
        logger.info("Loaded personal environment variables from .env.personal")

        # Get YOUR personal credentials
        self.api_key = os.getenv('PHEMEX_API_KEY')
        self.api_secret = os.getenv('PHEMEX_SECRET')
        self.use_testnet = os.getenv('USE_PHEMEX_TESTNET', '0') == '1'

        logger.info(f"Using YOUR API Key: {self.api_key[:8]}...")
        logger.info(f"Using YOUR Secret: {self.api_secret[:8]}...")
        logger.info(f"Testnet: {self.use_testnet}")

        # Initialize CCXT directly with YOUR credentials
        import ccxt
        self.exchange = ccxt.phemex({
            'apiKey': self.api_key,
            'secret': self.api_secret,
            'enableRateLimit': True,
            'test': self.use_testnet,
            'options': {
                'defaultType': 'swap',  # Use swap/contract trading for leverage
            }
        })
        
        # Personal account settings
        self.account_name = "PERSONAL_CASHCOLDGAME"
        self.default_leverage = int(os.getenv('DEFAULT_LEVERAGE', '1'))  # Conservative default
        self.btc_position_percent = float(os.getenv('BTC_POSITION_SIZE_PERCENT', '1.0'))
        self.ada_position_percent = float(os.getenv('ADA_POSITION_SIZE_PERCENT', '0.5'))
        
        logger.info(f"Initialized Personal Phemex service")
        logger.info(f"Account: {self.account_name}")
        logger.info(f"Default Leverage: {self.default_leverage}x")
        logger.info(f"BTC Position Size: {self.btc_position_percent}%")
        logger.info(f"ADA Position Size: {self.ada_position_percent}%")
    
    def calculate_position_size(self, symbol: str, price: float, risk_percent: float = None) -> float:
        """Calculate safe position size for leverage trading."""
        try:
            balance = self.exchange.fetch_balance()
            free_balance = balance['free']['USDT']

            # Use symbol-specific risk or default
            if risk_percent is None:
                if symbol == "BTC":
                    risk_percent = self.btc_position_percent / 100
                else:  # ADA
                    risk_percent = self.ada_position_percent / 100
            else:
                risk_percent = risk_percent / 100

            # For leverage trading, be ULTRA conservative to avoid TE_CANNOT_COVER_ESTIMATE_ORDER_LOSS
            leverage = self.default_leverage

            # Use only a tiny fraction of balance for ultra-safe trading
            # This ensures we have plenty of margin buffer
            safe_balance = free_balance * 0.1  # Use only 10% of balance

            # Calculate position value (much smaller to ensure margin coverage)
            position_value = safe_balance * risk_percent

            # Calculate quantity based on conservative position
            quantity = position_value / price

            # Apply Phemex minimum order sizes for contracts (very conservative)
            if symbol == "BTC":
                min_quantity = 0.0001  # Very small BTC contract size
                if quantity < min_quantity:
                    quantity = min_quantity
            elif symbol == "ADA":
                min_quantity = 1.0  # Very small ADA contract size
                if quantity < min_quantity:
                    quantity = min_quantity

            # Calculate required margin
            required_margin = (quantity * price) / leverage

            logger.info(f"💰 Ultra-safe leverage position sizing for {symbol}:")
            logger.info(f"   Free Balance: ${free_balance:.2f}")
            logger.info(f"   Safe Balance (10%): ${safe_balance:.2f}")
            logger.info(f"   Risk: {risk_percent*100:.1f}%")
            logger.info(f"   Leverage: {leverage}x")
            logger.info(f"   Position Value: ${position_value:.2f}")
            logger.info(f"   Price: ${price:.4f}")
            logger.info(f"   Quantity: {quantity:.6f} {symbol}")
            logger.info(f"   Required Margin: ${required_margin:.2f}")

            # Safety check: ensure we have enough margin
            if required_margin > free_balance * 0.8:  # Use max 80% of balance
                logger.warning(f"⚠️ Reducing position size due to margin requirements")
                quantity = (free_balance * 0.8 * leverage) / price
                required_margin = (quantity * price) / leverage
                logger.info(f"   Adjusted Quantity: {quantity:.6f} {symbol}")
                logger.info(f"   Adjusted Margin: ${required_margin:.2f}")

            return quantity

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            # Return safe minimums for contracts
            return 0.001 if symbol == "BTC" else 10.0
    
    def place_order(self, symbol: str, side: str, amount: float, price: float = None, order_type: str = "market") -> Dict[str, Any]:
        """Place order using correct CCXT parameters for Phemex contracts."""
        try:
            # Use correct Phemex perpetual contract symbols
            if symbol == "BTC":
                symbol_pair = "BTC/USD:BTC"  # BTC perpetual contract
            elif symbol == "ADA":
                symbol_pair = "ADA/USD:ADA"  # ADA perpetual contract
            else:
                symbol_pair = f"{symbol}/USD:{symbol}"
            
            logger.info(f"🔄 Placing {order_type} {side} order:")
            logger.info(f"   Symbol: {symbol_pair}")
            logger.info(f"   Amount: {amount:.6f}")
            logger.info(f"   Price: ${price:.4f}" if price else "Market Price")
            
            if order_type == "market":
                if side.upper() == "BUY":
                    order = self.exchange.create_market_buy_order(
                        symbol=symbol_pair,
                        amount=amount
                    )
                else:  # SELL
                    order = self.exchange.create_market_sell_order(
                        symbol=symbol_pair,
                        amount=amount
                    )
            else:  # limit order
                order = self.exchange.create_limit_order(
                    symbol=symbol_pair,
                    side=side.lower(),
                    amount=amount,
                    price=price
                )
            
            if order and order.get("id"):
                logger.info(f"✅ Order placed successfully:")
                logger.info(f"   Order ID: {order['id']}")
                logger.info(f"   Status: {order.get('status', 'Unknown')}")
                logger.info(f"   Amount: {order.get('amount', amount)}")
                logger.info(f"   Price: ${order.get('price', price)}")
                
                return {
                    "success": True,
                    "order_id": order["id"],
                    "status": order.get("status"),
                    "amount": order.get("amount", amount),
                    "price": order.get("price", price),
                    "symbol": symbol_pair,
                    "side": side,
                    "order": order
                }
            else:
                logger.error("❌ Order failed: No order ID returned")
                return {
                    "success": False,
                    "error": "No order ID returned",
                    "order": order
                }
                
        except Exception as e:
            logger.error(f"❌ Order failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_account_balance(self) -> Dict[str, Any]:
        """Get account balance information."""
        try:
            balance = self.exchange.fetch_balance()
            
            return {
                "free_usdt": balance['free']['USDT'],
                "used_usdt": balance['used']['USDT'],
                "total_usdt": balance['total']['USDT'],
                "free_btc": balance['free'].get('BTC', 0),
                "free_ada": balance['free'].get('ADA', 0),
                "account": self.account_name
            }
            
        except Exception as e:
            logger.error(f"Error getting balance: {str(e)}")
            return {"error": str(e)}
    
    def get_open_positions(self) -> Dict[str, Any]:
        """Get open positions."""
        try:
            positions = self.exchange.fetch_positions()
            open_positions = [pos for pos in positions if pos['contracts'] > 0]
            
            return {
                "positions": open_positions,
                "count": len(open_positions)
            }
            
        except Exception as e:
            logger.error(f"Error getting positions: {str(e)}")
            return {"error": str(e), "positions": [], "count": 0}
    
    def transfer_to_contract_wallet(self, amount: float) -> Dict[str, Any]:
        """Transfer USDT from spot to contract wallet for leverage trading."""
        try:
            logger.info(f"💸 Transferring ${amount:.2f} from Spot to Contract wallet")

            # Phemex transfer: spot to contract
            transfer = self.exchange.transfer('USDT', amount, 'spot', 'swap')

            if transfer and transfer.get('id'):
                logger.info(f"✅ Transfer successful: ${amount:.2f} moved to contract wallet")
                return {"success": True, "transfer_id": transfer['id'], "amount": amount}
            else:
                logger.error("❌ Transfer failed: No transfer ID returned")
                return {"success": False, "error": "No transfer ID"}

        except Exception as e:
            logger.error(f"❌ Transfer failed: {str(e)}")
            return {"success": False, "error": str(e)}

    def close_position(self, symbol: str) -> Dict[str, Any]:
        """Close an open position."""
        try:
            symbol_pair = f"{symbol}/USDT"
            positions = self.exchange.fetch_positions([symbol_pair])
            
            for position in positions:
                if position['contracts'] > 0:
                    # Close position by placing opposite order
                    side = "sell" if position['side'] == 'long' else "buy"
                    amount = position['contracts']
                    
                    order = self.exchange.create_market_order(
                        symbol=symbol_pair,
                        side=side,
                        amount=amount
                    )
                    
                    logger.info(f"✅ Closed {symbol} position")
                    return {"success": True, "order": order}
            
            return {"success": False, "error": f"No open position for {symbol}"}
            
        except Exception as e:
            logger.error(f"Error closing position: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def execute_trade(self, symbol: str, signal: str, price: float, confidence: float) -> Dict[str, Any]:
        """Execute a trade based on signal."""
        try:
            logger.info(f"🚀 Executing {signal} trade for {symbol}")
            
            # Calculate position size
            quantity = self.calculate_position_size(symbol, price)
            
            # Place order
            result = self.place_order(symbol, signal, quantity, price)
            
            if result.get("success"):
                logger.info(f"✅ Trade executed successfully for {symbol}")
                return {
                    "status": "success",
                    "symbol": symbol,
                    "signal": signal,
                    "quantity": quantity,
                    "price": price,
                    "confidence": confidence,
                    "order_id": result.get("order_id"),
                    "account": self.account_name
                }
            else:
                logger.error(f"❌ Trade failed for {symbol}: {result.get('error')}")
                return {
                    "status": "error",
                    "symbol": symbol,
                    "signal": signal,
                    "error": result.get("error"),
                    "account": self.account_name
                }
                
        except Exception as e:
            logger.error(f"Error executing trade: {str(e)}")
            return {
                "status": "error",
                "symbol": symbol,
                "signal": signal,
                "error": str(e),
                "account": self.account_name
            }
